[versions]
agp = "8.12.2"
composeEffects = "0.1.4"
haze = "1.6.10"
kotlin = "2.2.10"
coreKtx = "1.17.0"
junit = "4.14-SNAPSHOT"
junitVersion = "1.3.0"
espressoCore = "3.7.0"
lumo = "1.2.5"
activityCompose = "1.10.1"
composeBom = "2025.08.01"
coil = "3.3.0"
guia = "1.0.0-beta05"
kermit = "2.0.8"
koinAnnotations = "2.1.0"
ksp = "2.2.10-2.0.2"
kotlinxCoroutinesCore = "1.10.2"
kotlinxDatetime = "0.7.1"
kotlinxSerialization = "1.9.0"
lottie = "6.6.7"
lyricist = "1.7.0"
mmkv = "2.2.3"
orbit = "10.0.0"
lifecycle = "2.9.3"
resaca = "4.5.0"
koinBom = "4.1.0"
startupRuntime = "1.2.0"

[libraries]
compose-effects = { module = "com.github.skydoves:compose-effects", version.ref = "composeEffects" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }

androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-startup-runtime = { module = "androidx.startup:startup-runtime", version.ref = "startupRuntime" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycle" }
androidx-lifecycle-process = { group = "androidx.lifecycle", name = "lifecycle-process", version.ref = "lifecycle" }
androidx-lifecycle-runtime-compose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "lifecycle" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-compose-foundation-layout = { group = "androidx.compose.foundation", name = "foundation-layout" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-ui-viewbinding = { module = "androidx.compose.ui:ui-viewbinding" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }

coil = { module = "io.coil-kt.coil3:coil", version.ref = "coil" }
coil-compose = { module = "io.coil-kt.coil3:coil-compose", version.ref = "coil" }
coil-gif = { module = "io.coil-kt.coil3:coil-gif", version.ref = "coil" }
coil-network-okhttp = { module = "io.coil-kt.coil3:coil-network-okhttp", version.ref = "coil" }
guia = { module = "com.roudikk.guia:guia", version.ref = "guia" }
insert-koin-koin-ksp-compiler = { module = "io.insert-koin:koin-ksp-compiler", version.ref = "koinAnnotations" }
kermit = { module = "co.touchlab:kermit", version.ref = "kermit" }
koin-annotations = { module = "io.insert-koin:koin-annotations", version.ref = "koinAnnotations" }
koin-bom = { group = "io.insert-koin", name = "koin-bom", version.ref = "koinBom" }
koin-core = { group = "io.insert-koin", name = "koin-core" }
koin-android = { group = "io.insert-koin", name = "koin-android" }
koin-androidx-compose = { group = "io.insert-koin", name = "koin-androidx-compose" }
koin-ksp-compiler = { module = "io.insert-koin:koin-ksp-compiler" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutinesCore" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinxCoroutinesCore" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinxDatetime" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerialization" }
haze = { module = "dev.chrisbanes.haze:haze", version.ref = "haze" }
lottie-compose = { module = "com.airbnb.android:lottie-compose", version.ref = "lottie" }
lyricist = { module = "cafe.adriel.lyricist:lyricist", version.ref = "lyricist" }
lyricist-processor = { module = "cafe.adriel.lyricist:lyricist-processor", version.ref = "lyricist" }
mmkv = { module = "com.tencent:mmkv", version.ref = "mmkv" }
orbit-compose = { module = "org.orbit-mvi:orbit-compose", version.ref = "orbit" }
orbit-viewmodel = { module = "org.orbit-mvi:orbit-viewmodel", version.ref = "orbit" }
resaca = { module = "io.github.sebaslogen:resaca", version.ref = "resaca" }
resacakoin = { module = "io.github.sebaslogen:resacakoin", version.ref = "resaca" }


[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }

lumo = { id = "com.nomanr.plugin.lumo", version.ref = "lumo" }
