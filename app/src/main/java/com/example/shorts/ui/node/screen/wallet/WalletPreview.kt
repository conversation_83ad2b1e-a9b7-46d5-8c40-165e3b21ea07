package com.example.shorts.ui.node.screen.wallet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.shorts.ui.node.screen.wallet.components.*
import com.example.shorts.ui.theme.AppTheme
import java.math.BigDecimal

/**
 * 钱包页面预览
 */
@Preview(showBackground = true, backgroundColor = 0xFF1C1B1F)
@Composable
fun WalletPagePreview() {
  AppTheme {
    val sampleUiState = WalletUiState(
      balance = WalletBalance(
        amount = BigDecimal("3464.25"),
        currencySymbol = "$",
        currencyCode = "USD"
      ),
      withdrawAmountOptions = listOf(
        WithdrawAmountOption(BigDecimal("7000.00"), "$", false),
        WithdrawAmountOption(BigDecimal("12000.00"), "$", true),
        WithdrawAmountOption(BigDecimal("20000.00"), "$", false),
        WithdrawAmountOption(BigDecimal("30000.00"), "$", false),
        WithdrawAmountOption(BigDecimal("46000.00"), "$", false)
      ),
      selectedWithdrawAmount = BigDecimal("12000.00"),
      scrollingMessages = listOf(
        ScrollingMessage("1", "Alex", BigDecimal("5000.00"), "$"),
        ScrollingMessage("2", "Sarah", BigDecimal("12000.00"), "$"),
        ScrollingMessage("3", "Mike", BigDecimal("8500.00"), "$"),
        ScrollingMessage("4", "Emma", BigDecimal("15000.00"), "$"),
        ScrollingMessage("5", "John", BigDecimal("7200.00"), "$"),
        ScrollingMessage("6", "Lisa", BigDecimal("9800.00"), "$")
      ),
      isLoading = false,
      canWithdraw = true
    )
    
    Column(
      modifier = Modifier
        .fillMaxSize()
        .background(MaterialTheme.colorScheme.background)
        .padding(16.dp),
      verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
      // 顶部导航栏
      WalletTopBar(
        title = "My Wallet",
        onBackClick = { },
        onRightIconClick = { }
      )
      
      // 钱包余额卡片
      WalletBalanceCard(balance = sampleUiState.balance)
      
      // 提现金额选择
      WithdrawAmountSelector(
        options = sampleUiState.withdrawAmountOptions,
        onAmountSelected = { }
      )
      
      // 滚动弹幕
      ScrollingMessages(messages = sampleUiState.scrollingMessages)
      
      // 重要提示
      ImportantNotice()
    }
  }
}

/**
 * 单个组件预览
 */
@Preview(showBackground = true, backgroundColor = 0xFF1C1B1F)
@Composable
fun WalletComponentsPreview() {
  AppTheme {
    Column(
      modifier = Modifier
        .fillMaxSize()
        .background(MaterialTheme.colorScheme.background)
        .padding(16.dp),
      verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
      // 余额卡片
      WalletBalanceCard(
        balance = WalletBalance(
          amount = BigDecimal("3464.25"),
          currencySymbol = "$",
          currencyCode = "USD"
        )
      )
      
      // 金额选择器
      WithdrawAmountSelector(
        options = listOf(
          WithdrawAmountOption(BigDecimal("7000.00"), "$", false),
          WithdrawAmountOption(BigDecimal("12000.00"), "$", true)
        ),
        onAmountSelected = { }
      )
    }
  }
}
