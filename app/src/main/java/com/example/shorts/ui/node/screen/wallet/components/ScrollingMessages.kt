package com.example.shorts.ui.node.screen.wallet.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.shorts.ui.node.screen.wallet.ScrollingMessage
import com.example.shorts.ui.theme.AppTheme
import kotlinx.coroutines.delay
import java.math.BigDecimal
import kotlin.math.roundToInt

/**
 * 滚动弹幕组件
 */
@Composable
fun ScrollingMessages(
  messages: List<ScrollingMessage>,
  modifier: Modifier = Modifier
) {
  if (messages.isEmpty()) return

  Column(
    modifier = modifier
      .fillMaxWidth(),
    verticalArrangement = Arrangement.spacedBy(8.dp)
  ) {
    // 第一行弹幕 - 从右到左
    ScrollingMessageRow(
      messages = messages.take(3),
      direction = ScrollDirection.RightToLeft,
      delay = 0
    )

    // 第二行弹幕 - 从右到左，有延迟
    ScrollingMessageRow(
      messages = messages.drop(3).take(3),
      direction = ScrollDirection.RightToLeft,
      delay = 3000
    )
  }
}

/**
 * 滚动方向枚举
 */
private enum class ScrollDirection {
  RightToLeft, LeftToRight
}

/**
 * 单行滚动弹幕
 */
@Composable
private fun ScrollingMessageRow(
  messages: List<ScrollingMessage>,
  direction: ScrollDirection,
  delay: Int,
  modifier: Modifier = Modifier
) {
  if (messages.isEmpty()) return

  val screenWidth = LocalWindowInfo.current.containerSize.width
  val screenWidthF = screenWidth.toFloat()

  // 动画状态
  val infiniteTransition = rememberInfiniteTransition(label = "scrolling_transition")

  var startAnimation by remember { mutableStateOf(false) }

  // 延迟启动动画
  LaunchedEffect(Unit) {
    delay(delay.toLong())
    startAnimation = true
  }

  val animatedOffset by infiniteTransition.animateFloat(
    initialValue = screenWidthF,
    targetValue = if (startAnimation) {
      when (direction) {
        ScrollDirection.RightToLeft -> -screenWidthF
        ScrollDirection.LeftToRight -> screenWidthF
      }
    } else {
      screenWidthF
    },
    animationSpec = infiniteRepeatable(
      animation = tween(
        durationMillis = 10_000, // 15秒完成一次滚动
        easing = LinearEasing
      ),
      repeatMode = RepeatMode.Restart
    ),
    label = "scrolling_offset"
  )

  Box(
    modifier = modifier
  ) {
    Row(
      modifier = Modifier
        .offset {
          IntOffset(
            x = animatedOffset.roundToInt() + 16.dp.roundToPx(),
            y = 0
          )
        },
      horizontalArrangement = Arrangement.spacedBy(0.dp)
    ) {
      // 重复消息以实现无缝循环
      repeat(3) {
        messages.forEach { message ->
          ScrollingMessageItem(message = message)
        }
      }
    }
  }
}

/**
 * 单个滚动消息项
 */
@Composable
private fun ScrollingMessageItem(
  message: ScrollingMessage,
  modifier: Modifier = Modifier
) {
  Box(
    modifier = modifier
      .clip(CircleShape)
      .border(1.dp, MaterialTheme.colorScheme.secondary, CircleShape)
      .background(MaterialTheme.colorScheme.secondary.copy(.2f))
      .padding(horizontal = 12.dp, vertical = 4.dp)
  ) {
    Row(
      verticalAlignment = Alignment.CenterVertically,
      modifier = Modifier.wrapContentSize(unbounded = true)
    ) {
      // 庆祝图标占位符
      Box(
        modifier = Modifier
          .size(16.dp)
          .clip(RoundedCornerShape(8.dp))
          .background(Color(0xFFFFD700)) // 金色
      )

      Text(
        text = message.getDisplayText(),
        fontSize = 12.sp,
        fontWeight = FontWeight.Medium,
        color = Color.White,
        maxLines = 1,
      )
    }
  }
}

@Preview
@Composable
fun ScrollingMessagesPreview() {
  AppTheme {
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .background(MaterialTheme.colorScheme.background)
    ) {
      ScrollingMessages(
        messages = listOf(
          ScrollingMessage("1", "Alex", BigDecimal("5000.00"), "$"),
          ScrollingMessage("2", "Sarah", BigDecimal("12000.00"), "$"),
          ScrollingMessage("3", "Mike", BigDecimal("8500.00"), "$"),
          ScrollingMessage("4", "Emma", BigDecimal("15000.00"), "$"),
          ScrollingMessage("5", "John", BigDecimal("7200.00"), "$"),
          ScrollingMessage("6", "Lisa", BigDecimal("9800.00"), "$")
        )
      )
    }
  }
}
