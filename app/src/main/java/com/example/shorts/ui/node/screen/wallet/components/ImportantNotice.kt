package com.example.shorts.ui.node.screen.wallet.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.shorts.ui.theme.AppTheme

/**
 * 重要提示区域组件
 */
@Composable
fun ImportantNotice(
  modifier: Modifier = Modifier
) {
  Column(
    modifier = modifier
      .fillMaxWidth()
      .padding(horizontal = 16.dp)
  ) {
    // 标题
    Text(
      text = "Important Notice",
      fontSize = 16.sp,
      fontWeight = FontWeight.Medium,
      color = Color.White,
      modifier = Modifier.padding(bottom = 12.dp)
    )
    
    // 提示内容列表
    val noticeItems = listOf(
      "1. After earning tokens in the game, you can visit the Cash Out page to check your balance and apply for a withdrawal.",
      "2. All cash rewards will be transferred through the specified payment method.",
      "3. You must meet the conditions for each withdrawal amount before applying.",
      "4. Withdrawal requests are usually processed within 7 days.",
      "5. Errors in payment information can be corrected by contacting us, but users are responsible for any issues caused by incorrect details.",
      "6. Users are solely responsible for any taxes incurred. We reserve the right to withhold applicable taxes as required by law.",
      "7. If rewards cannot be issued for any reason, we reserve the right to withhold them."
    )
    
    Column(
      verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
      noticeItems.forEach { item ->
        NoticeItem(text = item)
      }
    }
  }
}

/**
 * 单个提示项
 */
@Composable
private fun NoticeItem(
  text: String,
  modifier: Modifier = Modifier
) {
  Text(
    text = text,
    fontSize = 13.sp,
    color = Color.White.copy(alpha = 0.8f),
    lineHeight = 18.sp,
    modifier = modifier.fillMaxWidth()
  )
}

@Preview
@Composable
fun ImportantNoticePreview() {
  AppTheme {
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .padding(16.dp)
    ) {
      ImportantNotice()
    }
  }
}
