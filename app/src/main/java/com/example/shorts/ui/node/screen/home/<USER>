package com.example.shorts.ui.node.screen.home

import android.os.Parcelable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import com.example.shorts.foundation.guia.ScreenNode
import com.example.shorts.foundation.mvi_ui_model.koinUiModel
import com.roudikk.guia.core.Navigator
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState


sealed interface HomeTab : Parcelable {
  @Parcelize
  data object Discover : HomeTab

  @Parcelize
  data object Shorts : HomeTab

  @Parcelize
  data object Tasks : HomeTab

  @Parcelize
  data object Games : HomeTab
}

@Parcelize
class HomeNode(
  private val initialTab: HomeTab = HomeTab.Shorts
) : ScreenNode("home") {
  @Composable
  override fun Content(navigator: Navigator) {
    val viewModel: HomeUiModel = koinUiModel { parametersOf(initialTab) }
    val uiState by viewModel.collectAsState()

    HomeContent(
      uiState = uiState,
      onTabSelected = viewModel::selectTab
    )
  }
}

@Composable
private fun HomeContent(
  uiState: HomeUiState,
  onTabSelected: (HomeTab) -> Unit
) {
  Scaffold(
    bottomBar = {
      HomeBottomBar(
        selectedTab = uiState.currentTab,
        onTabSelected = onTabSelected
      )
    }
  ) { paddingValues ->
    Box(
      modifier = Modifier
        .fillMaxSize()
        .padding(paddingValues)
    ) {
      when (uiState.currentTab) {
        HomeTab.Discover -> DiscoverScreen()
        HomeTab.Shorts -> ShortsScreen()
        HomeTab.Games -> GamesScreen()
        HomeTab.Tasks -> TasksScreen()
      }
    }
  }
}

@Composable
private fun DiscoverScreen() {
  Box(
    modifier = Modifier.fillMaxSize()
  ) {
    // Discover content placeholder
  }
}

@Composable
private fun ShortsScreen() {
  Box(
    modifier = Modifier.fillMaxSize()
  ) {
    // Shorts content placeholder
  }
}

@Composable
private fun GamesScreen() {
  Box(
    modifier = Modifier.fillMaxSize()
  ) {
    // Games content placeholder
  }
}

@Composable
private fun TasksScreen() {
  Box(
    modifier = Modifier.fillMaxSize()
  ) {
    // Tasks content placeholder
  }
}