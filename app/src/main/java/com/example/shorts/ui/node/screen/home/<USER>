package com.example.shorts.ui.node.screen.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.shorts.ui.icon.ValkyrieIcons
import com.example.shorts.ui.icon.HomeFilled
import com.example.shorts.ui.icon.HomeOutlined
import com.example.shorts.ui.icon.VideoFilled
import com.example.shorts.ui.icon.VideoOutlined
import com.example.shorts.ui.icon.GamepadFilled
import com.example.shorts.ui.icon.GamepadOutlined
import com.example.shorts.ui.icon.GiftFilled
import com.example.shorts.ui.icon.GiftOutlined
import com.example.shorts.ui.theme.AppTheme

@Composable
fun HomeBottomBar(
  selectedTab: HomeTab,
  onTabSelected: (HomeTab) -> Unit = {}
) {
  Box(
    modifier = Modifier
      .fillMaxWidth()
      .background(MaterialTheme.colorScheme.surface)
      .navigationBarsPadding()
  ) {
    Row(
      modifier = Modifier.fillMaxWidth(),
      verticalAlignment = Alignment.CenterVertically
    ) {
      BottomBarItem(
        icon = if (selectedTab == HomeTab.Discover) ValkyrieIcons.HomeFilled else ValkyrieIcons.HomeOutlined,
        label = "Discover",
        isSelected = selectedTab == HomeTab.Discover,
        onClick = { onTabSelected(HomeTab.Discover) },
        modifier = Modifier.weight(1f)
      )

      BottomBarItem(
        icon = if (selectedTab == HomeTab.Shorts) ValkyrieIcons.VideoFilled else ValkyrieIcons.VideoOutlined,
        label = "Shorts",
        isSelected = selectedTab == HomeTab.Shorts,
        onClick = { onTabSelected(HomeTab.Shorts) },
        modifier = Modifier.weight(1f)
      )

      BottomBarItem(
        icon = if (selectedTab == HomeTab.Games) ValkyrieIcons.GamepadFilled else ValkyrieIcons.GamepadOutlined,
        label = "Game",
        isSelected = selectedTab == HomeTab.Games,
        onClick = { onTabSelected(HomeTab.Games) },
        modifier = Modifier.weight(1f)
      )

      BottomBarItem(
        icon = if (selectedTab == HomeTab.Tasks) ValkyrieIcons.GiftFilled else ValkyrieIcons.GiftOutlined,
        label = "Task",
        isSelected = selectedTab == HomeTab.Tasks,
        onClick = { onTabSelected(HomeTab.Tasks) },
        modifier = Modifier.weight(1f)
      )
    }
  }
}

@Composable
private fun BottomBarItem(
  icon: ImageVector,
  label: String,
  isSelected: Boolean,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  Box(modifier = modifier.clickable { onClick() }, contentAlignment = Alignment.Center) {
    Column(
      horizontalAlignment = Alignment.CenterHorizontally,
      modifier = Modifier.padding(bottom = 6.dp, top = 4.dp)
    ) {
      Image(
        imageVector = icon,
        contentDescription = label,
        modifier = Modifier.size(34.dp),
      )

      Text(
        text = label,
        fontSize = 12.sp,
        fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
        lineHeight = 16.sp,
        color = if (isSelected) {
          MaterialTheme.colorScheme.primary
        } else {
          MaterialTheme.colorScheme.onSurfaceVariant
        }
      )
    }
  }
}

@Preview
@Composable
private fun HomeBottomBarPreview() {
  AppTheme {
    var selectedTab by remember { mutableStateOf<HomeTab>(HomeTab.Shorts) }
    HomeBottomBar(
      selectedTab = selectedTab,
      onTabSelected = { selectedTab = it }
    )
  }
}