package com.example.shorts.ui.node.screen.wallet

import java.math.BigDecimal

/**
 * 钱包余额数据
 */
data class WalletBalance(
  val amount: BigDecimal,
  val currencySymbol: String, // 货币符号，如 "$", "¥", "€" 等
  val currencyCode: String    // 货币代码，如 "USD", "CNY", "EUR" 等
)

/**
 * 提现金额选项
 */
data class WithdrawAmountOption(
  val amount: BigDecimal,
  val currencySymbol: String,
  val isSelected: Boolean = false
)

/**
 * 滚动弹幕消息
 */
data class ScrollingMessage(
  val id: String,
  val userName: String,
  val amount: BigDecimal,
  val currencySymbol: String
) {
  fun getDisplayText(): String = "🎉 Congratulations $userName on successfully withdrawing $currencySymbol${amount}"
}

/**
 * 钱包提现页面UI状态
 */
data class WalletUiState(
  val balance: WalletBalance = WalletBalance(
    amount = BigDecimal("3464.25"),
    currencySymbol = "$",
    currencyCode = "USD"
  ),
  val withdrawAmountOptions: List<WithdrawAmountOption> = emptyList(),
  val selectedWithdrawAmount: BigDecimal? = null,
  val scrollingMessages: List<ScrollingMessage> = emptyList(),
  val isLoading: Boolean = false,
  val canWithdraw: Boolean = true
)