package com.example.shorts.ui.node.screen.wallet

import com.example.shorts.foundation.mvi_ui_model.UiModel
import org.koin.android.annotation.KoinViewModel
import java.math.BigDecimal

@KoinViewModel
class WalletUiModel : UiModel<WalletUiState, WalletSideEffect>(WalletUiState()) {

  init {
    configure()
  }

  private fun configure() = intent {
    // 初始化提现金额选项
    val withdrawOptions = listOf(
      WithdrawAmountOption(BigDecimal("7000.00"), "$"),
      WithdrawAmountOption(BigDecimal("12000.00"), "$"),
      WithdrawAmountOption(BigDecimal("20000.00"), "$"),
      WithdrawAmountOption(BigDecimal("30000.00"), "$"),
      WithdrawAmountOption(BigDecimal("46000.00"), "$")
    )

    // 初始化滚动弹幕消息
    val messages = listOf(
      ScrollingMessage("1", "<PERSON>", BigDecimal("5000.00"), "$"),
      ScrollingMessage("2", "<PERSON>", BigDecimal("12000.00"), "$"),
      ScrollingMessage("3", "Mike", BigDecimal("8500.00"), "$"),
      ScrollingMessage("4", "Emma", BigDecimal("15000.00"), "$"),
      ScrollingMessage("5", "John", BigDecimal("7200.00"), "$"),
      ScrollingMessage("6", "Lisa", BigDecimal("9800.00"), "$")
    )

    reduce {
      state.copy(
        withdrawAmountOptions = withdrawOptions,
        scrollingMessages = messages
      )
    }
  }

  /**
   * 选择提现金额
   */
  fun selectWithdrawAmount(amount: BigDecimal) = intent {
    val updatedOptions = state.withdrawAmountOptions.map { option ->
      option.copy(isSelected = option.amount == amount)
    }

    reduce {
      state.copy(
        withdrawAmountOptions = updatedOptions,
        selectedWithdrawAmount = amount
      )
    }
  }

  /**
   * 执行提现
   */
  fun performWithdraw() = intent {
    val selectedAmount = state.selectedWithdrawAmount
    if (selectedAmount == null) {
      postSideEffect(WalletSideEffect.ShowError("Please select a withdrawal amount"))
      return@intent
    }

    if (selectedAmount > state.balance.amount) {
      postSideEffect(WalletSideEffect.ShowError("Insufficient balance"))
      return@intent
    }

    // 检查最小提现金额
    val minimumWithdraw = BigDecimal("7000.00")
    if (selectedAmount < minimumWithdraw) {
      postSideEffect(
        WalletSideEffect.ShowError(
          "According to the platform requirements, the minimum withdrawal amount is ${state.balance.currencySymbol}${minimumWithdraw}. You need ${state.balance.currencySymbol}${minimumWithdraw.subtract(selectedAmount)} more to reach the minimum withdrawal threshold."
        )
      )
      return@intent
    }

    reduce { state.copy(isLoading = true) }

    // 模拟网络请求
    try {
      // TODO: 实际的提现API调用
      kotlinx.coroutines.delay(2000)

      postSideEffect(
        WalletSideEffect.ShowWithdrawSuccess(
          "${state.balance.currencySymbol}${selectedAmount}"
        )
      )

      // 更新余额并重置选择
      val newBalance = state.balance.copy(
        amount = state.balance.amount.subtract(selectedAmount)
      )

      reduce {
        state.copy(
          balance = newBalance,
          isLoading = false,
          selectedWithdrawAmount = null,
          withdrawAmountOptions = state.withdrawAmountOptions.map { it.copy(isSelected = false) }
        )
      }
    } catch (e: Exception) {
      reduce { state.copy(isLoading = false) }
      postSideEffect(WalletSideEffect.ShowError(e.message ?: "Withdrawal failed"))
    }
  }

  /**
   * 刷新钱包数据
   */
  fun refreshWallet() = intent {
    reduce { state.copy(isLoading = true) }

    try {
      // TODO: 实际的API调用获取最新余额
      kotlinx.coroutines.delay(1000)

      // 模拟获取新的余额数据
      val updatedBalance = WalletBalance(
        amount = BigDecimal("3464.25"),
        currencySymbol = "$",
        currencyCode = "USD"
      )

      reduce {
        state.copy(
          balance = updatedBalance,
          isLoading = false
        )
      }
    } catch (e: Exception) {
      reduce { state.copy(isLoading = false) }
      postSideEffect(WalletSideEffect.ShowError("Failed to refresh wallet data"))
    }
  }

  /**
   * 清除选择
   */
  fun clearSelection() = intent {
    reduce {
      state.copy(
        selectedWithdrawAmount = null,
        withdrawAmountOptions = state.withdrawAmountOptions.map { it.copy(isSelected = false) }
      )
    }
  }

}