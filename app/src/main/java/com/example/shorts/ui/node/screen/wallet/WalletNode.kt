package com.example.shorts.ui.node.screen.wallet

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.example.shorts.foundation.guia.ScreenNode
import com.example.shorts.foundation.mvi_ui_model.koinUiModel
import com.example.shorts.ui.common.button.GradientButton
import com.example.shorts.ui.common.button.GradientButtonSize
import com.example.shorts.ui.common.button.GradientColors
import com.example.shorts.ui.node.screen.wallet.components.*
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Parcelize
class WalletNode : ScreenNode("wallet") {
  @Composable
  override fun Content(navigator: Navigator) {
    val viewModel: WalletUiModel = koinUiModel()
    val uiState by viewModel.collectAsState()
    val context = LocalContext.current

    // 处理副作用
    viewModel.collectSideEffect { sideEffect ->
      when (sideEffect) {
        is WalletSideEffect.ShowWithdrawSuccess -> {
          // TODO: 显示成功提示
        }
        is WalletSideEffect.ShowError -> {
          // TODO: 显示错误提示
        }
        WalletSideEffect.NavigateBack -> {
          navigator.pop()
        }
      }
    }

    WalletContent(
      uiState = uiState,
      onBackClick = { navigator.pop() },
      onAmountSelected = viewModel::selectWithdrawAmount,
      onWithdrawClick = viewModel::performWithdraw
    )
  }
}

/**
 * 钱包页面内容
 */
@Composable
private fun WalletContent(
  uiState: WalletUiState,
  onBackClick: () -> Unit,
  onAmountSelected: (java.math.BigDecimal) -> Unit,
  onWithdrawClick: () -> Unit
) {
  Scaffold(
    topBar = {
      WalletTopBar(
        title = "My Wallet",
        onBackClick = onBackClick
      )
    },
    bottomBar = {
      // 底部提现按钮
      Box(
        modifier = Modifier
          .fillMaxWidth()
          .padding(16.dp)
      ) {
        GradientButton(
          text = if (uiState.isLoading) "Processing..." else "Cash out",
          onClick = onWithdrawClick,
          modifier = Modifier.fillMaxWidth(),
          size = GradientButtonSize.Large,
          gradient = GradientColors.Purple,
          enabled = !uiState.isLoading && uiState.selectedWithdrawAmount != null
        )
      }
    }
  ) { paddingValues ->
    Column(
      modifier = Modifier
        .fillMaxSize()
        .padding(paddingValues)
        .verticalScroll(rememberScrollState()),
      verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
      Spacer(modifier = Modifier.height(8.dp))

      // 钱包余额卡片
      WalletBalanceCard(balance = uiState.balance)

      // 提现金额选择
      WithdrawAmountSelector(
        options = uiState.withdrawAmountOptions,
        onAmountSelected = onAmountSelected
      )

      // 滚动弹幕
      ScrollingMessages(messages = uiState.scrollingMessages)

      // 重要提示
      ImportantNotice()

      // 底部间距，为底部按钮留出空间
      Spacer(modifier = Modifier.height(80.dp))
    }
  }
}

