package com.example.shorts.ui.common.button


import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.CutCornerShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ProvideTextStyle
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.shorts.ui.theme.AppTheme


// 按钮尺寸枚举
enum class GradientButtonSize {
  Small, Medium, Large
}

// 预定义的渐变色
object GradientColors {
  // 紫色渐变 (类似设计稿中的紫色按钮)
  val Purple = Brush.horizontalGradient(
    colors = listOf(
      Color(0xFF844FDD), // 紫色
      Color(0xFFD364FF)  // 较亮的紫色
    )
  )

  // 黄色渐变 (类似设计稿中的黄色按钮)
  val Yellow = Brush.horizontalGradient(
    colors = listOf(
      Color(0xFFFBBF24), // 黄色
      Color(0xFFF59E0B)  // 橙黄色
    )
  )
}

// 获取按钮尺寸配置
private data class ButtonSizeConfig(
  val height: Dp,
  val horizontalPadding: Dp,
  val fontSize: TextUnit
)

private fun getButtonSizeConfig(size: GradientButtonSize): ButtonSizeConfig {
  return when (size) {
    GradientButtonSize.Small -> ButtonSizeConfig(
      height = 32.dp,
      horizontalPadding = 12.dp,
      fontSize = 12.sp
    )

    GradientButtonSize.Medium -> ButtonSizeConfig(
      height = 40.dp,
      horizontalPadding = 16.dp,
      fontSize = 14.sp
    )

    GradientButtonSize.Large -> ButtonSizeConfig(
      height = 48.dp,
      horizontalPadding = 20.dp,
      fontSize = 16.sp
    )
  }
}

// 获取默认形状
private fun getDefaultShape(size: GradientButtonSize): Shape {
  return when (size) {
    GradientButtonSize.Small -> RoundedCornerShape(8.dp)
    GradientButtonSize.Medium -> RoundedCornerShape(10.dp)
    GradientButtonSize.Large -> RoundedCornerShape(12.dp)
  }
}

/**
 * 渐变色按钮组件
 *
 * @param onClick 点击事件
 * @param modifier 修饰符
 * @param size 按钮尺寸
 * @param gradient 渐变色Brush
 * @param shape 自定义形状，默认使用圆角矩形
 * @param enabled 是否启用
 * @param contentColor 内容颜色
 * @param content 按钮内容
 */
@Composable
fun GradientButton(
  onClick: () -> Unit,
  modifier: Modifier = Modifier,
  size: GradientButtonSize = GradientButtonSize.Medium,
  gradient: Brush = GradientColors.Purple,
  shape: Shape? = null,
  enabled: Boolean = true,
  contentColor: Color = Color.White,
  content: @Composable RowScope.() -> Unit
) {
  val sizeConfig = getButtonSizeConfig(size)
  val buttonShape = shape ?: getDefaultShape(size)
  val interactionSource = remember { MutableInteractionSource() }

  Row(
    modifier = modifier
      .height(sizeConfig.height)
      .clip(buttonShape)
      .background(
        brush = if (enabled) gradient else Brush.horizontalGradient(
          listOf(Color.Gray, Color.Gray)
        )
      )
      .clickable(
        interactionSource = interactionSource,
        indication = ripple(color = contentColor.copy(alpha = 0.3f)),
        enabled = enabled,
        onClick = onClick
      )
      .padding(horizontal = sizeConfig.horizontalPadding),
    horizontalArrangement = Arrangement.Center,
    verticalAlignment = Alignment.CenterVertically
  ) {
    CompositionLocalProvider(
      LocalContentColor provides if (enabled) contentColor else contentColor.copy(alpha = 0.6f)
    ) {
      ProvideTextStyle(
        value = LocalTextStyle.current.copy(
          fontSize = sizeConfig.fontSize,
          fontWeight = FontWeight.Medium
        )
      ) {
        content()
      }
    }
  }
}

/**
 * 文本渐变按钮的便捷方法
 */
@Composable
fun GradientButton(
  text: String,
  onClick: () -> Unit,
  modifier: Modifier = Modifier,
  size: GradientButtonSize = GradientButtonSize.Medium,
  gradient: Brush = GradientColors.Purple,
  shape: Shape? = null,
  enabled: Boolean = true,
  contentColor: Color = Color.White
) {
  GradientButton(
    onClick = onClick,
    modifier = modifier,
    size = size,
    gradient = gradient,
    shape = shape,
    enabled = enabled,
    contentColor = contentColor
  ) {
    Text(text = text)
  }
}

/**
 * 带描边的渐变按钮
 */
@Composable
fun OutlinedGradientButton(
  onClick: () -> Unit,
  modifier: Modifier = Modifier,
  size: GradientButtonSize = GradientButtonSize.Medium,
  gradient: Brush = GradientColors.Purple,
  shape: Shape? = null,
  borderWidth: Dp = 1.dp,
  borderColor: Color = Color.White.copy(alpha = 0.3f),
  enabled: Boolean = true,
  contentColor: Color = Color.White,
  content: @Composable RowScope.() -> Unit
) {
  val sizeConfig = getButtonSizeConfig(size)
  val buttonShape = shape ?: getDefaultShape(size)
  val interactionSource = remember { MutableInteractionSource() }

  Row(
    modifier = modifier
      .height(sizeConfig.height)
      .clip(buttonShape)
      .background(
        brush = if (enabled) gradient else Brush.horizontalGradient(
          listOf(Color.Gray, Color.Gray)
        )
      )
      .border(
        width = borderWidth,
        color = if (enabled) borderColor else borderColor.copy(alpha = 0.5f),
        shape = buttonShape
      )
      .clickable(
        interactionSource = interactionSource,
        indication = ripple(color = contentColor.copy(alpha = 0.3f)),
        enabled = enabled,
        onClick = onClick
      )
      .padding(horizontal = sizeConfig.horizontalPadding),
    horizontalArrangement = Arrangement.Center,
    verticalAlignment = Alignment.CenterVertically
  ) {
    CompositionLocalProvider(
      LocalContentColor provides if (enabled) contentColor else contentColor.copy(alpha = 0.6f)
    ) {
      ProvideTextStyle(
        value = LocalTextStyle.current.copy(
          fontSize = sizeConfig.fontSize,
          fontWeight = FontWeight.Medium
        )
      ) {
        content()
      }
    }
  }
}

@Preview
@Composable
fun GradientButtonExamples() {
  AppTheme {
    Column(
      modifier = Modifier
        .fillMaxSize()
        .padding(16.dp),
      verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
      Text(
        text = "渐变按钮示例",
        style = MaterialTheme.typography.headlineMedium
      )

      // 不同尺寸的紫色渐变按钮
      Text("紫色渐变按钮 - 不同尺寸:")
      GradientButton(
        text = "小按钮",
        onClick = { },
        size = GradientButtonSize.Small,
        gradient = GradientColors.Purple
      )

      GradientButton(
        text = "中等按钮",
        onClick = { },
        size = GradientButtonSize.Medium,
        gradient = GradientColors.Purple
      )

      GradientButton(
        text = "大按钮",
        onClick = { },
        size = GradientButtonSize.Large,
        gradient = GradientColors.Purple
      )

      Spacer(modifier = Modifier.height(8.dp))

      // 黄色渐变按钮
      Text("黄色渐变按钮:")
      GradientButton(
        text = "Withdraw",
        onClick = { },
        size = GradientButtonSize.Medium,
        gradient = GradientColors.Yellow,
        contentColor = Color.Black
      )

      // 带描边的按钮
      Text("带描边的渐变按钮:")
      OutlinedGradientButton(
        onClick = { },
        size = GradientButtonSize.Medium,
        gradient = GradientColors.Purple
      ) {
        Text("Watch")
      }

      // 禁用状态
      Text("禁用状态:")
      GradientButton(
        text = "禁用按钮",
        onClick = { },
        size = GradientButtonSize.Medium,
        gradient = GradientColors.Purple,
        enabled = false
      )

      Spacer(modifier = Modifier.height(8.dp))

      // 自定义形状示例
      Text("自定义形状示例:")

      // 圆形按钮
      GradientButton(
        text = "圆形",
        onClick = { },
        size = GradientButtonSize.Medium,
        gradient = GradientColors.Purple,
        shape = CircleShape
      )

      // 切角形状按钮
      GradientButton(
        text = "切角",
        onClick = { },
        size = GradientButtonSize.Medium,
        gradient = GradientColors.Yellow,
        shape = CutCornerShape(8.dp),
        contentColor = Color.Black
      )

      // 自定义圆角按钮
      GradientButton(
        text = "大圆角",
        onClick = { },
        size = GradientButtonSize.Medium,
        gradient = GradientColors.Purple,
        shape = RoundedCornerShape(20.dp)
      )

      // 带自定义形状的描边按钮
      OutlinedGradientButton(
        onClick = { },
        size = GradientButtonSize.Medium,
        gradient = GradientColors.Yellow,
        shape = CutCornerShape(topStart = 16.dp, bottomEnd = 16.dp),
        contentColor = Color.Black,
        borderColor = Color.Black.copy(alpha = 0.3f)
      ) {
        Text("自定义描边")
      }
    }
  }

}