package com.example.shorts.ui.node.screen.wallet.components

import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.shorts.ui.node.screen.wallet.WithdrawAmountOption
import com.example.shorts.ui.theme.AppTheme
import java.math.BigDecimal

/**
 * 提现金额选择器组件
 */
@Composable
fun WithdrawAmountSelector(
  title: String = "Select withdrawal amount",
  options: List<WithdrawAmountOption>,
  onAmountSelected: (BigDecimal) -> Unit,
  modifier: Modifier = Modifier
) {
  Column(
    modifier = modifier
  ) {
    // 标题
    Text(
      text = title,
      fontSize = 14.sp,
      fontWeight = FontWeight.Medium,
      color = Color.White,
      modifier = Modifier.padding(bottom = 12.dp)
    )

    // 金额选项网格
    LazyVerticalGrid(
      columns = GridCells.Fixed(3),
      horizontalArrangement = Arrangement.spacedBy(8.dp),
      verticalArrangement = Arrangement.spacedBy(8.dp),
      modifier = Modifier.fillMaxWidth()
    ) {
      items(options) { option ->
        WithdrawAmountButton(
          option = option,
          onClick = { onAmountSelected(option.amount) }
        )
      }
    }
  }
}

/**
 * 单个提现金额按钮
 */
@Composable
private fun WithdrawAmountButton(
  option: WithdrawAmountOption,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val (backgroundColor, borderColor) = if (option.isSelected) {
    MaterialTheme.colorScheme.primary.copy(.2f) to MaterialTheme.colorScheme.primary
  } else {
    MaterialTheme.colorScheme.surface to Color.Transparent
  }

  Box(
    modifier = modifier
      .fillMaxWidth()
      .height(60.dp)
      .clip(RoundedCornerShape(12.dp))
      .background(color = backgroundColor)
      .border(
        width = 1.dp,
        color = borderColor,
        shape = RoundedCornerShape(12.dp)
      )
      .clickable { onClick() },
    contentAlignment = Alignment.Center
  ) {
    Row(
      verticalAlignment = Alignment.CenterVertically,
      horizontalArrangement = Arrangement.Center
    ) {
      // 钱包图标占位符
      Box(
        modifier = Modifier
          .size(20.dp)
          .clip(RoundedCornerShape(4.dp))
          .background(
            if (option.isSelected) Color.White.copy(alpha = 0.9f)
            else Color(0xFF4CAF50)
          )
      )

      Spacer(modifier = Modifier.width(4.dp))

      // 金额文本
      Text(
        text = "${option.currencySymbol}${option.amount}",
        fontSize = 13.sp,
        fontWeight = FontWeight.Medium,
        color = if (option.isSelected) Color.White else Color.White.copy(alpha = 0.9f),
        maxLines = 1,
        modifier = Modifier.basicMarquee()
      )
    }
  }
}

@Preview
@Composable
fun WithdrawAmountSelectorPreview() {
  AppTheme {
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .background(MaterialTheme.colorScheme.background)
    ) {
      WithdrawAmountSelector(
        title = "Select withdrawal amount",
        options = listOf(
          WithdrawAmountOption(BigDecimal("7000.00"), "$", false),
          WithdrawAmountOption(BigDecimal("12000.00"), "$", true),
          WithdrawAmountOption(BigDecimal("20000.00"), "$", false),
          WithdrawAmountOption(BigDecimal("30000.00"), "$", false),
          WithdrawAmountOption(BigDecimal("46000.00"), "$", false)
        ),
        onAmountSelected = { }
      )
    }
  }
}
