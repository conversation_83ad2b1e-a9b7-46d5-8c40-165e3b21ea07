package com.example.shorts.ui.node.screen.wallet.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.shorts.ui.node.screen.wallet.WalletBalance
import com.example.shorts.ui.theme.AppTheme
import java.math.BigDecimal

/**
 * 钱包余额卡片组件
 */
@Composable
fun WalletBalanceCard(
  balance: WalletBalance,
  modifier: Modifier = Modifier
) {
  Card(
    modifier = modifier
      .fillMaxWidth()
      .padding(horizontal = 16.dp),
    shape = RoundedCornerShape(16.dp),
    colors = CardDefaults.cardColors(
      containerColor = MaterialTheme.colorScheme.surface
    ),
    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(20.dp),
      verticalAlignment = Alignment.CenterVertically
    ) {
      // 左侧钱包图标占位符
      WalletIconPlaceholder()
      
      Spacer(modifier = Modifier.width(16.dp))
      
      // 中间余额显示
      Column(
        modifier = Modifier.weight(1f)
      ) {
        Text(
          text = "${balance.currencySymbol}${balance.amount}",
          fontSize = 24.sp,
          fontWeight = FontWeight.Bold,
        )
        
        Spacer(modifier = Modifier.height(4.dp))
      }
      
      // 右侧装饰性钱包图形
      WalletDecorationGraphic()
    }
  }
}

/**
 * 钱包图标占位符
 */
@Composable
private fun WalletIconPlaceholder() {
  Box(
    modifier = Modifier
      .size(48.dp)
      .clip(RoundedCornerShape(12.dp))
      .background(
        brush = Brush.linearGradient(
          colors = listOf(
            Color(0xFF4CAF50), // 绿色
            Color(0xFF8BC34A)  // 浅绿色
          )
        )
      ),
    contentAlignment = Alignment.Center
  ) {
    // 钱包图标的简单表示
    Box(
      modifier = Modifier
        .size(24.dp)
        .clip(RoundedCornerShape(4.dp))
        .background(Color.White.copy(alpha = 0.9f))
    )
  }
}

/**
 * 右侧装饰性钱包图形
 */
@Composable
private fun WalletDecorationGraphic() {
  Box(
    modifier = Modifier.size(80.dp, 60.dp)
  ) {
    // 钱包主体
    Box(
      modifier = Modifier
        .size(60.dp, 40.dp)
        .align(Alignment.Center)
        .clip(RoundedCornerShape(8.dp))
        .background(
          brush = Brush.linearGradient(
            colors = listOf(
              Color(0xFF4CAF50),
              Color(0xFF66BB6A)
            )
          )
        )
    )
    
    // 钱包上的装饰线条
    Box(
      modifier = Modifier
        .width(40.dp)
        .height(2.dp)
        .align(Alignment.Center)
        .background(Color.White.copy(alpha = 0.6f))
    )
    
    // 右上角的星星装饰
    Box(
      modifier = Modifier
        .size(12.dp)
        .align(Alignment.TopEnd)
        .clip(RoundedCornerShape(6.dp))
        .background(Color(0xFFFFD700)) // 金色
    )
  }
}

@Preview
@Composable
fun WalletBalanceCardPreview() {
  AppTheme {
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .background(MaterialTheme.colorScheme.background)
        .padding(16.dp)
    ) {
      WalletBalanceCard(
        balance = WalletBalance(
          amount = BigDecimal("3464.25"),
          currencySymbol = "$",
          currencyCode = "USD"
        )
      )
    }
  }
}
