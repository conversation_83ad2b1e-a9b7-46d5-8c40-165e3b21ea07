package com.example.shorts.foundation.lyricist

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.text.intl.Locale
import cafe.adriel.lyricist.Strings
import com.example.shorts.LocaleSupport
import com.example.shorts.foundation.lyricist.i18n.AppStringsEn
import kotlinx.coroutines.flow.MutableStateFlow

object Locales {
  const val EN = "en"

}

val runtimeLanguageTagFlow = MutableStateFlow(Locale.current.toLanguageTag())

@Suppress("ObjectPropertyName")
private var _globalAppStrings: AppStrings? = null
val globalAppStrings get() = _globalAppStrings ?: AppStringsEn

fun configureGlobalStrings(appStrings: AppStrings) {
  _globalAppStrings = appStrings
}

@Composable
fun ConfigureGlobalStringsEffect(appStrings: AppStrings) {
  LaunchedEffect(appStrings) {
    configureGlobalStrings(appStrings)
  }
}

fun matchStrings(languageTag: String): AppStrings {
  val compatibleLocale: java.util.Locale =
    LocaleSupport.compatibleLanguage(java.util.Locale.forLanguageTag(languageTag))
      ?: LocaleSupport.En

  return Strings[compatibleLocale.toLanguageTag()] ?: AppStringsEn
}

data class AppStrings(
  val ok: String = "OK",
  val appName: String = "Short Player",
)